using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using PlayerFAP.DropResource;
using PlayerFAP.Pooling.Systems;
using Watermelon;
using Watermelon.SquadShooter;

namespace PlayerFAP.DropResource.Systems
{
    /// <summary>
    /// System that handles drop animations including scale-up and movement
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(DropPoolingSystem))]
    public partial struct DropAnimationSystem : ISystem
    {
        private EntityQuery scaleAnimationQuery;
        private EntityQuery movementAnimationQuery;
        private EntityQuery newDropItemsQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for entities with scale animation
            scaleAnimationQuery = SystemAPI.QueryBuilder()
                .WithAll<DropAnimationComponent, LocalTransform>()
                .WithNone<DropAnimationCompleteTag>()
                .Build();

            // Query for entities with movement animation
            movementAnimationQuery = SystemAPI.QueryBuilder()
                .WithAll<DropMovementComponent, LocalTransform>()
                .WithNone<DropMovementCompleteTag>()
                .Build();

            // Query for newly spawned drop items that need initialization
            newDropItemsQuery = SystemAPI.QueryBuilder()
                .WithAll<DropItemTag, DropAnimationComponent, LocalTransform>()
                .WithNone<DropAnimationInitializedTag>()
                .Build();

            state.RequireForUpdate(scaleAnimationQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var currentTime = (float)SystemAPI.Time.ElapsedTime;
            var deltaTime = SystemAPI.Time.DeltaTime;

            // Get ECB for deferred operations
            var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Initialize newly spawned drop items first
            if (!newDropItemsQuery.IsEmpty)
            {
                var initializeJob = new InitializeDropAnimationJob
                {
                    ECB = ecb.AsParallelWriter(),
                    CurrentTime = currentTime,
                    DropItemLookup = SystemAPI.GetComponentLookup<DropItemComponent>(false),
                    DropBounceLookup = SystemAPI.GetComponentLookup<DropBounceComponent>(false),
                    DropLifetimeLookup = SystemAPI.GetComponentLookup<DropLifetimeComponent>(false),
                    DropMovementLookup = SystemAPI.GetComponentLookup<DropMovementComponent>(false)
                };
                state.Dependency = initializeJob.ScheduleParallel(newDropItemsQuery, state.Dependency);
            }

            // Complete the initialization job before starting animation jobs to avoid ECB conflicts
            state.Dependency.Complete();

            // Get separate ECBs for each animation job to avoid conflicts
            var scaleEcb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);
            var movementEcb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Process scale animations first
            var scaleAnimationJob = new ProcessScaleAnimationJob
            {
                ECB = scaleEcb.AsParallelWriter(),
                CurrentTime = currentTime,
                DeltaTime = deltaTime
            };

            var scaleJobHandle = scaleAnimationJob.ScheduleParallel(scaleAnimationQuery, state.Dependency);

            // Process movement animations after scale animations (to avoid LocalTransform conflicts)
            var movementAnimationJob = new ProcessMovementAnimationJob
            {
                ECB = movementEcb.AsParallelWriter(),
                CurrentTime = currentTime,
                DeltaTime = deltaTime
            };

            // Make movement job depend on scale job to avoid LocalTransform write conflicts
            var movementJobHandle = movementAnimationJob.ScheduleParallel(movementAnimationQuery, scaleJobHandle);

            // Set final dependency
            state.Dependency = movementJobHandle;
        }
    }

    /// <summary>
    /// Job that initializes drop animation components for newly spawned items
    /// </summary>
    [BurstCompile]
    public partial struct InitializeDropAnimationJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public ComponentLookup<DropItemComponent> DropItemLookup;
        [ReadOnly] public ComponentLookup<DropBounceComponent> DropBounceLookup;
        [ReadOnly] public ComponentLookup<DropLifetimeComponent> DropLifetimeLookup;
        [ReadOnly] public ComponentLookup<DropMovementComponent> DropMovementLookup;

        public void Execute(
            Entity entity,
            [EntityIndexInQuery] int entityInQueryIndex,
            ref DropAnimationComponent dropAnimation,
            ref LocalTransform transform)
        {
            // Initialize animation timing
            dropAnimation.StartTime = CurrentTime;
            dropAnimation.IsAnimating = true;
            dropAnimation.IsActive = true;
            dropAnimation.CurrentScale = dropAnimation.StartScale; // Should be 0

            // Set initial scale to zero for scale-up animation
            transform.Scale = dropAnimation.StartScale;

            // Initialize drop item spawn time if DropItemComponent exists
            if (DropItemLookup.HasComponent(entity))
            {
                var dropItem = DropItemLookup[entity];
                dropItem.SpawnTime = CurrentTime;
                ECB.SetComponent(entityInQueryIndex, entity, dropItem);
            }

            // Initialize bounce component if present
            if (DropBounceLookup.HasComponent(entity))
            {
                var bounceComponent = DropBounceLookup[entity];
                bounceComponent.StartY = transform.Position.y;
                bounceComponent.BounceTime = 0f;
                bounceComponent.CurrentOffset = 0f;
                ECB.SetComponent(entityInQueryIndex, entity, bounceComponent);
            }

            // Initialize lifetime component if present
            if (DropLifetimeLookup.HasComponent(entity))
            {
                var lifetimeComponent = DropLifetimeLookup[entity];
                lifetimeComponent.SpawnTime = CurrentTime;
                lifetimeComponent.ShouldDestroy = false;
                ECB.SetComponent(entityInQueryIndex, entity, lifetimeComponent);
            }

            // Initialize movement component if present (for slight random movement)
            if (DropMovementLookup.HasComponent(entity))
            {
                var movementComponent = DropMovementLookup[entity];
                movementComponent.StartPosition = transform.Position;
                // Add slight random offset for natural movement using Unity.Mathematics.Random
                var random = Unity.Mathematics.Random.CreateFromIndex((uint)(entity.Index + (int)(CurrentTime * 1000)));
                var randomOffset = new float3(
                    random.NextFloat(-0.5f, 0.5f),
                    0f,
                    random.NextFloat(-0.5f, 0.5f)
                );
                movementComponent.TargetPosition = transform.Position + randomOffset;
                movementComponent.StartTime = CurrentTime + 0.1f; // Start movement after scale animation begins
                movementComponent.IsActive = true;
                ECB.SetComponent(entityInQueryIndex, entity, movementComponent);
            }

            // Mark as initialized to prevent re-processing
            ECB.AddComponent<DropAnimationInitializedTag>(entityInQueryIndex, entity);
        }
    }

    /// <summary>
    /// Job to process scale animations
    /// </summary>
    [BurstCompile]
    public partial struct ProcessScaleAnimationJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public float DeltaTime;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref LocalTransform transform, ref DropAnimationComponent animation)
        {
            if (!animation.IsAnimating)
                return;

            // Calculate animation progress
            float elapsedTime = CurrentTime - animation.StartTime;
            float progress = math.clamp(elapsedTime / animation.Duration, 0f, 1f);

            // Apply easing curve
            float easedProgress = ApplyEasing(progress, animation.AnimationType);

            // Calculate current scale
            float currentScale = math.lerp(animation.StartScale, animation.TargetScale, easedProgress);

            // Update transform scale
            transform.Scale = currentScale;

            // Check if animation is complete
            if (progress >= 1f)
            {
                animation.IsAnimating = false;
                transform.Scale = animation.TargetScale;
                
                // Add completion tag
                ECB.AddComponent<DropAnimationCompleteTag>(entityInQueryIndex, entity);
            }
        }

        private float ApplyEasing(float t, DropAnimationType animationType)
        {
            switch (animationType)
            {
                case DropAnimationType.ScaleUp:
                    // Ease out back for bouncy scale-up effect
                    return EaseOutBack(t);
                case DropAnimationType.Linear:
                    return t;
                case DropAnimationType.EaseInOut:
                    return EaseInOutQuad(t);
                default:
                    return t;
            }
        }

        private float EaseOutBack(float t)
        {
            const float c1 = 1.70158f;
            const float c3 = c1 + 1f;
            return 1f + c3 * math.pow(t - 1f, 3f) + c1 * math.pow(t - 1f, 2f);
        }

        private float EaseInOutQuad(float t)
        {
            return t < 0.5f ? 2f * t * t : 1f - math.pow(-2f * t + 2f, 2f) / 2f;
        }
    }

    /// <summary>
    /// Job to process movement animations
    /// </summary>
    [BurstCompile]
    public partial struct ProcessMovementAnimationJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public float DeltaTime;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref LocalTransform transform, ref DropMovementComponent movement)
        {
            if (!movement.IsActive)
                return;

            // Calculate movement progress
            float elapsedTime = CurrentTime - movement.StartTime;
            float progress = math.clamp(elapsedTime / movement.Duration, 0f, 1f);

            // Apply easing for smooth movement
            float easedProgress = EaseOutQuad(progress);

            // Calculate current position
            float3 currentPosition = math.lerp(movement.StartPosition, movement.TargetPosition, easedProgress);

            // Add slight bounce effect
            float bounceHeight = 0.2f * math.sin(progress * math.PI);
            currentPosition.y += bounceHeight;

            // Update transform position
            transform.Position = currentPosition;

            // Check if movement is complete
            if (progress >= 1f)
            {
                movement.IsActive = false;
                transform.Position = movement.TargetPosition;
                
                // Add completion tag
                ECB.AddComponent<DropMovementCompleteTag>(entityInQueryIndex, entity);
            }
        }

        private float EaseOutQuad(float t)
        {
            return 1f - (1f - t) * (1f - t);
        }
    }

    /// <summary>
    /// System to handle drop pickup detection and auto-pickup
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(DropAnimationSystem))]
    public partial struct DropPickupSystem : ISystem
    {
        private EntityQuery dropPickupQuery;
        private EntityQuery playerQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for drop entities with pickup component
            dropPickupQuery = SystemAPI.QueryBuilder()
                .WithAll<DropPickupComponent, DropResourceComponent, LocalTransform>()
                .WithNone<DropPickedUpTag>()
                .Build();

            // Query for player entities (assuming player has PlayerTag)
            playerQuery = SystemAPI.QueryBuilder()
                .WithAll<PlayerTag, LocalTransform>()
                .Build();

            state.RequireForUpdate(dropPickupQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var currentTime = (float)SystemAPI.Time.ElapsedTime;

            // Get player position
            var playerPosition = GetPlayerPosition(ref state);
            if (!playerPosition.HasValue)
                return;

            // Get ECB for deferred operations
            var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Process pickup detection
            var pickupJob = new ProcessDropPickupJob
            {
                ECB = ecb.AsParallelWriter(),
                CurrentTime = currentTime,
                PlayerPosition = playerPosition.Value
            };

            state.Dependency = pickupJob.ScheduleParallel(dropPickupQuery, state.Dependency);
        }

        private float3? GetPlayerPosition(ref SystemState state)
        {
            if (playerQuery.IsEmpty)
                return null;

            var playerEntities = playerQuery.ToEntityArray(Allocator.Temp);
            if (playerEntities.Length > 0)
            {
                var playerTransform = SystemAPI.GetComponent<LocalTransform>(playerEntities[0]);
                playerEntities.Dispose();
                return playerTransform.Position;
            }
            playerEntities.Dispose();
            return null;
        }
    }

    /// <summary>
    /// Job to process drop pickup detection
    /// </summary>
    [BurstCompile]
    public partial struct ProcessDropPickupJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public float3 PlayerPosition;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            in DropPickupComponent pickup, in DropResourceComponent dropResource, 
            in LocalTransform transform)
        {
            // Check if pickup delay has passed
            float timeSinceSpawn = CurrentTime - dropResource.SpawnTime;
            if (timeSinceSpawn < pickup.PickupDelay)
                return;

            // Check distance to player
            float distanceToPlayer = math.distance(transform.Position, PlayerPosition);
            bool shouldPickup = distanceToPlayer <= pickup.PickupRadius;

            // Check for auto-pickup
            if (!shouldPickup && timeSinceSpawn >= pickup.AutoPickupDelay)
            {
                shouldPickup = true;
            }

            if (shouldPickup)
            {
                // Mark as picked up
                ECB.AddComponent<DropPickedUpTag>(entityInQueryIndex, entity);
                
                // Create pickup event
                var pickupEvent = ECB.CreateEntity(entityInQueryIndex);
                ECB.AddComponent(entityInQueryIndex, pickupEvent, new DropPickupEvent
                {
                    DropType = dropResource.DropType,
                    CurrencyType = dropResource.CurrencyType,
                    WeaponType = dropResource.WeaponType,
                    Amount = dropResource.Amount,
                    PickupTime = CurrentTime,
                    PlayerPosition = PlayerPosition
                });
            }
        }
    }

    /// <summary>
    /// Tag component to mark completed scale animations
    /// </summary>
    public struct DropAnimationCompleteTag : IComponentData { }

    /// <summary>
    /// Tag component to mark completed movement animations
    /// </summary>
    public struct DropMovementCompleteTag : IComponentData { }

    /// <summary>
    /// Tag component to mark picked up drops
    /// </summary>
    public struct DropPickedUpTag : IComponentData { }

    /// <summary>
    /// Tag component for player entities
    /// </summary>
    public struct PlayerTag : IComponentData { }

    /// <summary>
    /// Tag component to mark drop items that have been initialized
    /// </summary>
    public struct DropAnimationInitializedTag : IComponentData { }

    /// <summary>
    /// Event component for drop pickup
    /// </summary>
    public struct DropPickupEvent : IComponentData
    {
        public DropableItemType DropType;
        public CurrencyType CurrencyType;
        public WeaponSubModuleState WeaponType;
        public int Amount;
        public float PickupTime;
        public float3 PlayerPosition;
    }
}
