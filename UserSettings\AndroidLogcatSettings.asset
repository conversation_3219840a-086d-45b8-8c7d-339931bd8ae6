{"m_SelectedDeviceId": "R96WC00SZ5K", "m_SelectedProcess": {"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 12882, "exited": false}, "m_SelectedPriority": 0, "m_KnownProcessesForSerialization": [{"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 31945, "exited": true}, {"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 8074, "exited": true}, {"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 10905, "exited": true}, {"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 12110, "exited": true}, {"deviceId": "R96WC00SZ5K", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 12882, "exited": false}, {"deviceId": "emulator-5554", "name": "com.google.android.apps.nexuslauncher", "processId": 1515, "exited": false}, {"deviceId": "emulator-5554", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 8647, "exited": true}, {"deviceId": "emulator-5554", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 10404, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 23345, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 16538, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 16540, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 16541, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 23661, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 23664, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 14942, "exited": false}, {"deviceId": "55acb8d6", "name": "com.HybridCasualTempleCleanTestECSGPURefactorAnimSystem", "processId": 25169, "exited": false}], "m_Tags": {"m_Entries": [{"m_Name": "Filter by all listed tags", "m_Selected": false}, {"m_Name": "No Filter", "m_Selected": false}, {"m_Name": "", "m_Selected": false}, {"m_Name": "Tag Control...", "m_Selected": false}, {"m_Name": "", "m_Selected": false}, {"m_Name": "Unity", "m_Selected": true}, {"m_Name": "CRASH", "m_Selected": true}]}, "m_ExtraWindowState": {"Type": 0, "Height": 0.0}, "m_MemoryViewerState": {"MemoryWindowWidth": 170.0, "MemoryTypeEnabled": [true, true, true, true, true, true, true, true, true, true], "MemoryGroup": 2, "AutoCapture": true}, "m_FilterOptions": {"m_Filter": "", "m_UseRegularExpressions": false, "m_MatchCase": false}, "m_SymbolPaths": [], "m_CaptureVideoSettings": {"TimeLimitEnabled": false, "TimeLimit": 180, "VideoSizeEnabled": false, "VideoSizeX": 1280, "VideoSizeY": 720, "BitRateEnabled": false, "BitRateK": 20000, "DisplayIdEnabled": false, "DisplayId": ""}, "m_ScreenCaptureSettings": {"Mode": 0, "m_LastSaveLocation": ["D:\\UnityProject\\PersianArts\\Hybrid6.2", "D:\\UnityProject\\PersianArts\\Hybrid6.2"]}, "m_QueryLayoutSettings": {"LastLayoutSaveLocation": "", "LastScreenshotSaveLocation": ""}, "m_InputSettings": {"ShiftModifier": false, "SendText": "", "PosixKillSignal": 0, "TargetProcess": {"deviceId": "", "name": "", "processId": 0, "exited": false}}, "m_AutoScroll": 2}