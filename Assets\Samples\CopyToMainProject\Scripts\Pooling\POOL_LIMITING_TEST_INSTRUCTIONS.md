# Pool Limiting System Test Instructions

## Overview
This document provides instructions for testing the new pool limiting functionality that prevents unlimited spawning when pools reach their maximum capacity.

## Changes Made

### 1. PoolDatabaseEntry Structure
- Added `initialPoolSize`: Number of entities to pre-instantiate when pool is initialized
- Added `maxPoolSize`: Maximum number of entities that can exist in this pool (0 = unlimited)
- Kept legacy `poolSize` field for backward compatibility

### 2. PoolingSystem Updates
- Uses `initialPoolSize` for pre-instantiation during pool initialization
- Uses `maxPoolSize` for enforcing pool capacity limits
- Fallback to legacy `poolSize` if new fields are not set

### 3. SpawnerSystem Updates
- Added `IsPoolAtCapacity()` method to check pool capacity before sending spawn requests
- Stops sending `PoolSpawnRequest` when pool reaches maximum capacity
- Logs capacity warnings in debug mode

## Testing Steps

### Step 1: Configure Pool Database
1. Open `Assets\Samples\CopyToMainProject\Data\PoolDatabase.asset`
2. For an existing entry (e.g., Enemy1), set:
   - `initialPoolSize`: 5 (entities to pre-create)
   - `maxPoolSize`: 10 (maximum entities allowed)
   - Leave `poolSize` as is for backward compatibility

### Step 2: Test Pool Initialization
1. Start the game/scene
2. Check the Entity Debugger or Hierarchy
3. Verify that 5 Enemy1 entities are pre-instantiated (initialPoolSize)
4. These should be marked as `Pooled` and not `InUse`

### Step 3: Test Pool Capacity Limiting
1. Create a spawner that spawns Enemy1 entities
2. Set the spawner to spawn more than 10 entities (maxPoolSize)
3. Observe that:
   - Only 10 Enemy1 entities exist at any time
   - SpawnerSystem stops sending requests after reaching capacity
   - Debug logs show "Pool at capacity" messages

### Step 4: Test Backward Compatibility
1. Create a new PoolDatabaseEntry with only `poolSize` set (leave initialPoolSize and maxPoolSize at 0)
2. Verify that the system uses `poolSize` for both initialization and capacity limiting

### Step 5: Test Unlimited Pools
1. Set `maxPoolSize` to 0 for an entry
2. Verify that the pool can grow beyond the initial size without limits

## Expected Behavior

### Before Fix
- SpawnerSystem would continue sending PoolSpawnRequest indefinitely
- PoolingSystem would create new entities beyond pool size limits
- Memory usage would grow unbounded

### After Fix
- SpawnerSystem checks pool capacity before sending requests
- Pool growth is limited to maxPoolSize
- System gracefully handles capacity limits without errors

## Debug Information

### Console Logs
- "SpawnerSystem: Pool at capacity for typeHash X (EnemyType), skipping spawn request"
- "SpawnerSystem: Requesting spawn with typeHash X for EnemyType"

### Entity Debugger
- Check entities with `PoolTypeComponent` to see current pool counts
- Verify `Pooled` and `InUse` component states
- Monitor total entity count for each pool type

## Troubleshooting

### Issue: Pool not limiting
- Check that maxPoolSize > 0 in PoolDatabase
- Verify SpawnerSystem is calling IsPoolAtCapacity()
- Check debug logs for capacity warnings

### Issue: No pre-instantiation
- Verify initialPoolSize > 0 in PoolDatabase
- Check that PoolingSystem.InitializePools() is running
- Look for PoolInitializedTag on pool manager entity

### Issue: Backward compatibility broken
- Ensure legacy poolSize field is still set
- Check GetInitialPoolSize() and GetMaxPoolSize() methods
- Verify fallback logic in PoolingSystem

## Performance Notes
- IsPoolAtCapacity() creates a temporary query each call
- Consider caching pool counts for high-frequency spawning
- Monitor performance in Entity Debugger during heavy spawning
