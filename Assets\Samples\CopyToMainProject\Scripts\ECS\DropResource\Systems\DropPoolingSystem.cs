using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using Watermelon.SquadShooter;
using PlayerFAP.DropResource;
using PlayerFAP.DropResource.Systems;
using Rukhanka.Test.Pooling;
using Unity.Burst.Intrinsics;
using Watermelon;
using Watermelon.LevelSystem;
using Random = Unity.Mathematics.Random;

namespace PlayerFAP.Pooling.Systems
{
    /// <summary>
    /// System that integrates drop spawning with the existing pooling system
    /// Processes DropRequest components from DamageSystem and creates drops via PoolingSystem
    ///
    /// Performance Notes:
    /// - ProcessDropRequestsJob cannot use <PERSON>urst due to managed LevelsDatabase dependency
    /// - ProcessDropSpawnsJob uses Burst compilation for optimal performance
    /// - System processes both new DropRequest entities and legacy DropSpawnTag entities
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(PlayerFAP.Systems.Health.DamageSystem))]
    public partial struct DropPoolingSystem : ISystem
    {
        private EntityQuery dropRequestQuery;
        private EntityQuery dropSpawnQuery;
        private EntityQuery dropContainerQuery;
        private EntityQuery poolingSystemQuery;
        private EntityQuery poolManagerQuery;
        private EntityQuery dropConfigQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<BeginSimulationEntityCommandBufferSystem.Singleton>();
            // Query for drop requests from DamageSystem (only enabled ones)
            dropRequestQuery = SystemAPI.QueryBuilder()
                .WithAll<DropRequest>()
                .Build();

            // Query for drop spawn requests
            dropSpawnQuery = SystemAPI.QueryBuilder()
                .WithAll<DropSpawnTag, PoolSpawnRequest, DropDataComponent>()
                .WithNone<DropPoolProcessedTag>()
                .Build();

            // Query for drop containers
            dropContainerQuery = SystemAPI.QueryBuilder()
                .WithAll<DropContainerTag, DropContainerData>()
                .Build();

            // Query for pooling system entities
            poolingSystemQuery = SystemAPI.QueryBuilder()
                .WithAll<PoolingSystemData>()
                .Build();

            // Query for pool manager entities (entities with PoolSpawnRequest buffer)
            poolManagerQuery = SystemAPI.QueryBuilder()
                .WithAll<PoolSpawnRequest>()
                .Build();

            // Query for drop configuration singleton
            dropConfigQuery = SystemAPI.QueryBuilder()
                .WithAll<DropConfigurationComponent, DropConfigurationSingleton>()
                .WithAll<EnemyDropConfigurationElement, DropTypeHashElement>()
                .Build();

            state.RequireForUpdate(dropRequestQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get ECB for deferred operations
            var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Debug logging
            int dropRequestCount = dropRequestQuery.CalculateEntityCount();
            int dropConfigCount = dropConfigQuery.CalculateEntityCount();

            if (dropRequestCount > 0 || dropConfigCount > 0)
            {
                //Debug.Log($"DropPoolingSystem: DropRequests={dropRequestCount}, DropConfigs={dropConfigCount}");
            }

            // First, process drop requests from DamageSystem
            if (!dropRequestQuery.IsEmpty && !dropConfigQuery.IsEmpty)
            {
                // Find or create pool manager entity
                Entity poolManagerEntity = Entity.Null;
                if (!poolManagerQuery.IsEmpty)
                {
                    poolManagerEntity = poolManagerQuery.GetSingletonEntity();
                    //Debug.Log($"Found existing pool manager entity: {poolManagerEntity.Index}");
                }
                else
                {
                    // Create a pool manager entity if none exists
                    poolManagerEntity = ecb.CreateEntity();
                    ecb.AddBuffer<PoolSpawnRequest>(poolManagerEntity);
                    //Debug.Log($"Created new pool manager entity: {poolManagerEntity.Index}");
                }

                // Get drop configuration data
                var configEntity = dropConfigQuery.GetSingletonEntity();
                var dropConfig = SystemAPI.GetComponent<DropConfigurationComponent>(configEntity);
                var enemyConfigs = SystemAPI.GetBuffer<EnemyDropConfigurationElement>(configEntity);
                var dropTypeHashes = SystemAPI.GetBuffer<DropTypeHashElement>(configEntity);

                var processDropRequestsJob = new ProcessDropRequestsJob
                {
                    ECB = ecb.AsParallelWriter(),
                    RandomSeed = (uint)UnityEngine.Random.Range(1, int.MaxValue),
                    CurrentTime = (float)SystemAPI.Time.ElapsedTime,
                    PoolManagerEntity = poolManagerEntity,
                    DropConfig = dropConfig,
                    EnemyConfigs = enemyConfigs,
                    DropTypeHashes = dropTypeHashes,
                    EntityType = SystemAPI.GetEntityTypeHandle(),
                    DropRequestType = SystemAPI.GetComponentTypeHandle<DropRequest>(true)
                };

                state.Dependency = processDropRequestsJob.ScheduleParallel(dropRequestQuery, state.Dependency);
            }

            // Then process existing drop spawn requests
            if (!dropSpawnQuery.IsEmpty)
            {
                var processDropSpawnsJob = new ProcessDropSpawnsJob
                {
                    ECB = ecb.AsParallelWriter(),
                    RandomSeed = (uint)UnityEngine.Random.Range(1, int.MaxValue),
                    CurrentTime = (float)SystemAPI.Time.ElapsedTime,
                    DeltaTime = SystemAPI.Time.DeltaTime
                };

                state.Dependency = processDropSpawnsJob.ScheduleParallel(dropSpawnQuery, state.Dependency);
            }
        }
    }

    /// <summary>
    /// Job to process DropRequest components from DamageSystem and create pool spawn requests
    /// Now uses value types only - can be Burst compiled for performance
    /// </summary>
    // [BurstCompile] // Temporarily disabled for debug logging
    public struct ProcessDropRequestsJob : IJobChunk
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public uint RandomSeed;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public Entity PoolManagerEntity; // Existing pool manager entity

        // Drop configuration data (value types only)
        [ReadOnly] public DropConfigurationComponent DropConfig;
        [ReadOnly] public DynamicBuffer<EnemyDropConfigurationElement> EnemyConfigs;
        [ReadOnly] public DynamicBuffer<DropTypeHashElement> DropTypeHashes;

        [ReadOnly] public EntityTypeHandle EntityType;
        [ReadOnly] public ComponentTypeHandle<DropRequest> DropRequestType;

        public void Execute(in ArchetypeChunk chunk, int unfilteredChunkIndex, bool useEnabledMask, in v128 chunkEnabledMask)
        {
            var entities = chunk.GetNativeArray(EntityType);
            var dropRequests = chunk.GetNativeArray(ref DropRequestType);

            for (int i = 0; i < chunk.Count; i++)
            {
                var entity = entities[i];
                var dropRequest = dropRequests[i];

                ProcessSingleDropRequest(entity, unfilteredChunkIndex * chunk.Count + i, dropRequest);
            }
        }

        private void ProcessSingleDropRequest(Entity entity, int entityInQueryIndex, DropRequest dropRequest)
        {
            var random = Random.CreateFromIndex((uint)(entityInQueryIndex + RandomSeed));

            // Debug logging (remove Burst attribute temporarily for this)
            #if UNITY_EDITOR
            Debug.Log($"Processing drop request for enemy type: {dropRequest.EnemyType} at position: {dropRequest.DeathPosition}");
            #endif

            // Get enemy drop configuration
            if (!DropConfigurationHelper.TryGetEnemyDropConfiguration(EnemyConfigs, dropRequest.EnemyType, out var enemyDropConfig))
            {
                #if UNITY_EDITOR
                Debug.LogWarning($"No drop configuration found for enemy type: {dropRequest.EnemyType}");
                #endif
                // No drop configuration found, remove request and return
                ECB.DestroyEntity(entityInQueryIndex, entity);
                return;
            }

            // Calculate number of currency drops
            int currencyAmount = random.NextInt(enemyDropConfig.MinCurrencyDrop, enemyDropConfig.MaxCurrencyDrop + 1);
            if (dropRequest.IsElite)
            {
                currencyAmount = (int)(currencyAmount * enemyDropConfig.EliteCurrencyMultiplier);
            }

            // Use the existing pool manager entity passed from the main thread
            // Add spawn requests to the existing pool manager's buffer
            #if UNITY_EDITOR
            Debug.Log($"Adding {currencyAmount} currency drops to pool manager entity {PoolManagerEntity.Index}");
            #endif

            // Create currency drops
            for (int i = 0; i < currencyAmount; i++)
            {
                // Get the specific currency type hash based on enemy configuration
                if (!DropConfigurationHelper.TryGetCurrencyTypeHash(DropTypeHashes, enemyDropConfig.DefaultCurrencyType, out uint currencyPoolHash))
                {
                    // Fallback to generic currency hash if specific type not found
                    if (!DropConfigurationHelper.TryGetDropTypeHash(DropTypeHashes, DropableItemType.Currency, out currencyPoolHash))
                    {
                        // Skip if no currency hash found
                        continue;
                    }
                }

                // Calculate random position within drop radius
                float angle = random.NextFloat(0f, math.PI * 2f);
                float distance = random.NextFloat(0f, enemyDropConfig.DropRadius);
                float3 offset = new float3(
                    math.cos(angle) * distance,
                    0f,
                    math.sin(angle) * distance
                );

                var spawnRequest = new PoolSpawnRequest
                {
                    TypeHash = currencyPoolHash,
                    Position = dropRequest.DeathPosition + offset,
                    Rotation = quaternion.identity,
                    Scale = 0f, // Start with zero scale for animation
                    RequestingEntity = dropRequest.DeadEnemyEntity
                };
                ECB.AppendToBuffer(entityInQueryIndex, PoolManagerEntity, spawnRequest);

                #if UNITY_EDITOR
                Debug.Log($"Added currency drop request: TypeHash={currencyPoolHash}, Position={spawnRequest.Position}");
                Debug.Log($"DropPoolingSystem: Requesting currency drop with hash {currencyPoolHash} for {enemyDropConfig.DefaultCurrencyType}");
                #endif
            }

            // Check for weapon card drop
            if (random.NextFloat() < enemyDropConfig.WeaponCardDropChance)
            {
                // Get the specific weapon type hash based on enemy configuration
                if (DropConfigurationHelper.TryGetWeaponTypeHash(DropTypeHashes, enemyDropConfig.PreferredWeaponType, out uint weaponCardPoolHash) ||
                    DropConfigurationHelper.TryGetDropTypeHash(DropTypeHashes, DropableItemType.WeaponCard, out weaponCardPoolHash))
                {
                    float angle = random.NextFloat(0f, math.PI * 2f);
                    float distance = random.NextFloat(0f, enemyDropConfig.DropRadius);
                    float3 offset = new float3(
                        math.cos(angle) * distance,
                        0f,
                        math.sin(angle) * distance
                    );

                    ECB.AppendToBuffer(entityInQueryIndex, PoolManagerEntity, new PoolSpawnRequest
                    {
                        TypeHash = weaponCardPoolHash,
                        Position = dropRequest.DeathPosition + offset,
                        Rotation = quaternion.identity,
                        Scale = 0f,
                        RequestingEntity = dropRequest.DeadEnemyEntity
                    });
                }
            }

            // Check for heal drop (if configured)
            if (random.NextFloat() < enemyDropConfig.HealthDropChance)
            {
                if (DropConfigurationHelper.TryGetDropTypeHash(DropTypeHashes, DropableItemType.Heal, out uint healPoolHash))
                {
                    float angle = random.NextFloat(0f, math.PI * 2f);
                    float distance = random.NextFloat(0f, enemyDropConfig.DropRadius);
                    float3 offset = new float3(
                        math.cos(angle) * distance,
                        0f,
                        math.sin(angle) * distance
                    );

                    ECB.AppendToBuffer(entityInQueryIndex, PoolManagerEntity, new PoolSpawnRequest
                    {
                        TypeHash = healPoolHash,
                        Position = dropRequest.DeathPosition + offset,
                        Rotation = quaternion.identity,
                        Scale = 0f,
                        RequestingEntity = dropRequest.DeadEnemyEntity
                    });
                }
            }

            // Remove the processed drop request
            ECB.DestroyEntity(entityInQueryIndex, entity);
        }
    }

    /// <summary>
    /// Job to process drop spawn requests and integrate with pooling system
    /// </summary>
    [BurstCompile]
    public partial struct ProcessDropSpawnsJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public uint RandomSeed;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public float DeltaTime;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref DynamicBuffer<PoolSpawnRequest> poolRequestBuffer, in DropDataComponent dropData, in DropSpawnTag dropSpawnTag)
        {
            var random = Random.CreateFromIndex((uint)(entityInQueryIndex + RandomSeed));

            // Mark as processed
            ECB.AddComponent<DropPoolProcessedTag>(entityInQueryIndex, entity);

            // Process each pool spawn request in the buffer
            for (int i = 0; i < poolRequestBuffer.Length; i++)
            {
                var poolRequest = poolRequestBuffer[i];

                // Calculate spawn position within radius
                var spawnPosition = CalculateDropPosition(poolRequest, ref random);

                // Update pool spawn request with calculated position
                poolRequest.Position = spawnPosition;
                poolRequest.Scale = 0; // Start with zero scale for animation
                poolRequestBuffer[i] = poolRequest;

                // Create the actual drop entity through pooling system
                var dropEntity = ECB.CreateEntity(entityInQueryIndex);

                // Add pooling system buffer element (this integrates with existing PoolingSystem)
                var poolBuffer = ECB.AddBuffer<PoolSpawnRequest>(entityInQueryIndex, dropEntity);
                poolBuffer.Add(poolRequest);

                // Add drop-specific components to the spawned entity
                ECB.AddComponent(entityInQueryIndex, dropEntity, new DropResourceComponent
                {
                    DropType = dropData.DropType,
                    CurrencyType = dropData.CurrencyType,
                    WeaponType = dropData.WeaponType,
                    Amount = dropData.Amount,
                    SpawnTime = CurrentTime,
                    IsPickedUp = false
                });

                // Add transform component
                ECB.AddComponent(entityInQueryIndex, dropEntity, LocalTransform.FromPositionRotationScale(
                    spawnPosition, quaternion.identity, 0f));

                // Add animation component for scale-up effect
                ECB.AddComponent(entityInQueryIndex, dropEntity, new DropAnimationComponent
                {
                    StartTime = CurrentTime,
                    Duration = 0.5f, // Default animation duration
                    StartScale = 0f,
                    TargetScale = 1f,
                    AnimationType = DropAnimationType.ScaleUp,
                    IsActive = true,
                    IsAnimating = true
                });

                // Add movement component for radius spreading
                var targetPosition = CalculateTargetPosition(spawnPosition, ref random);
                ECB.AddComponent(entityInQueryIndex, dropEntity, new DropMovementComponent
                {
                    StartPosition = spawnPosition,
                    TargetPosition = targetPosition,
                    StartTime = CurrentTime,
                    Duration = 0.3f, // Movement duration
                    IsActive = true
                });

                // Add pickup component
                ECB.AddComponent(entityInQueryIndex, dropEntity, new DropPickupComponent
                {
                    PickupDelay = 0.5f, // Delay before pickup is allowed
                    AutoPickupDelay = 10f, // Auto pickup after this time
                    PickupRadius = 1f
                });

                // Add drop resource tag
                ECB.AddComponent<DropResourceTag>(entityInQueryIndex, dropEntity);
            }
        }

        private float3 CalculateDropPosition(in PoolSpawnRequest poolRequest, ref Random random)
        {
            // Get base position from parent drop request
            var basePosition = poolRequest.Position;
            
            // Add small random offset for initial spawn
            var randomOffset = new float3(
                random.NextFloat(-0.1f, 0.1f),
                0f,
                random.NextFloat(-0.1f, 0.1f)
            );

            return basePosition + randomOffset;
        }

        private float3 CalculateTargetPosition(float3 startPosition, ref Random random)
        {
            // Calculate target position within drop radius
            var angle = random.NextFloat(0f, 2f * math.PI);
            var distance = random.NextFloat(0.5f, 1.5f); // Drop radius
            
            var offset = new float3(
                math.cos(angle) * distance,
                0f,
                math.sin(angle) * distance
            );

            return startPosition + offset;
        }
    }

    /// <summary>
    /// Tag component to mark drop pool requests as processed
    /// </summary>
    public struct DropPoolProcessedTag : IComponentData { }

    /// <summary>
    /// Component for drop resource entities
    /// </summary>
    public struct DropResourceComponent : IComponentData
    {
        public DropableItemType DropType;
        public CurrencyType CurrencyType;
        public WeaponSubModuleState WeaponType;
        public int Amount;
        public float SpawnTime;
        public bool IsPickedUp;
    }

    /// <summary>
    /// Component for drop movement behavior
    /// </summary>
    public struct DropMovementComponent : IComponentData
    {
        public float3 StartPosition;
        public float3 TargetPosition;
        public float StartTime;
        public float Duration;
        public bool IsActive;
    }

    /// <summary>
    /// Component for drop pickup behavior
    /// </summary>
    public struct DropPickupComponent : IComponentData
    {
        public float PickupDelay;
        public float AutoPickupDelay;
        public float PickupRadius;
    }

    /// <summary>
    /// Tag component for drop resource entities
    /// </summary>
    public struct DropResourceTag : IComponentData { }

    /// <summary>
    /// Placeholder for pooling system data (should match existing system)
    /// </summary>
    public struct PoolingSystemData : IComponentData
    {
        public int MaxPoolSize;
        public int CurrentPoolSize;
        public bool IsInitialized;
    }
}
