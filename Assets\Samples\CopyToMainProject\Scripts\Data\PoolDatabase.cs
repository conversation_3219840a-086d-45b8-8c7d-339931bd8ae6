using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR && ODIN_INSPECTOR
using Sirenix.OdinInspector;
using System.Linq;
using System.Reflection;
#endif

namespace Data
{
    /// <summary>
    /// Serializable entry that maps an enum type/value to a prefab and desired pool size.
    /// </summary>
    [Serializable]
    public struct PoolDatabaseEntry
    {
        [Header("Enum Configuration")]
        #if UNITY_EDITOR && ODIN_INSPECTOR
        [ValueDropdown("GetAvailableEnumTypes")]
        [OnValueChanged("OnEnumTypeChanged")]
        #endif
        [Tooltip("Full type name of the enum (e.g., 'MyProject.EnemyType')")]
        public string enumTypeName;
        
        #if UNITY_EDITOR && ODIN_INSPECTOR
        [ValueDropdown("GetAvailableEnumValues")]
        [ShowIf("@!string.IsNullOrEmpty(enumTypeName)")]
        #endif
        [Tooltip("Name of the enum value (e.g., 'Goblin')")]
        public string enumValueName;
        
        [Header("Pool Settings")]
        [Tooltip("Prefab to be pooled")]
        public GameObject prefab;

        [Tooltip("Number of entities to pre-instantiate when pool is initialized")]
        [Min(0)] public int initialPoolSize;

        [Tooltip("Maximum number of entities that can exist in this pool (0 = unlimited)")]
        [Min(0)] public int maxPoolSize;

        [Header("Legacy (Deprecated)")]
        [Tooltip("DEPRECATED: Use initialPoolSize and maxPoolSize instead. This field is kept for backward compatibility.")]
        [Min(0)] public int poolSize;
        
        [Header("Runtime Info (Read-Only)")]
        [SerializeField, Tooltip("Calculated hash for this enum type/value combination")]
#if UNITY_EDITOR && ODIN_INSPECTOR
        [ReadOnly]
#endif
        private uint calculatedHash;

        #if UNITY_EDITOR && ODIN_INSPECTOR
        [Button("Update Hash")]
        [PropertySpace(SpaceBefore = 5)]
        private void UpdateHashButton()
        {
            UpdateCalculatedHash();
        }

        private IEnumerable<ValueDropdownItem<string>> GetAvailableEnumTypes()
        {
            var enumTypes = new List<ValueDropdownItem<string>>();
            
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                try
                {
                    foreach (var type in assembly.GetTypes())
                    {
                        if (type.IsEnum && type.GetCustomAttribute<UseInPoolAttribute>() != null)
                        {
                            var attribute = type.GetCustomAttribute<UseInPoolAttribute>();
                            var displayName = $"{attribute.Category}/{type.Name}";
                            if (!string.IsNullOrEmpty(attribute.Description))
                                displayName += $" - {attribute.Description}";
                            
                            enumTypes.Add(new ValueDropdownItem<string>(displayName, type.FullName));
                        }
                    }
                }
                catch (ReflectionTypeLoadException)
                {
                    // Skip assemblies that can't be loaded
                }
            }
            
            return enumTypes.OrderBy(x => x.Text);
        }

        private IEnumerable<ValueDropdownItem<string>> GetAvailableEnumValues()
        {
            if (string.IsNullOrEmpty(enumTypeName))
                return new ValueDropdownItem<string>[0];

            try
            {
                var enumType = Type.GetType(enumTypeName);
                if (enumType != null && enumType.IsEnum)
                {
                    return Enum.GetNames(enumType)
                        .Select(name => new ValueDropdownItem<string>(name, name))
                        .OrderBy(x => x.Text);
                }
            }
            catch
            {
                // Invalid type name
            }
            
            return new ValueDropdownItem<string>[0];
        }

        private void OnEnumTypeChanged()
        {
            // Clear enum value when type changes
            enumValueName = string.Empty;
            UpdateCalculatedHash();
        }
        #endif
        
        /// <summary>
        /// Calculate hash for this enum type/value combination
        /// </summary>
        public uint GetCalculatedHash()
        {
            if (!string.IsNullOrEmpty(enumTypeName) && !string.IsNullOrEmpty(enumValueName))
            {
                var typeHash = enumTypeName.GetHashCode();
                var valueHash = enumValueName.GetHashCode();
                var result = (uint)(typeHash ^ valueHash);
                
                #if UNITY_EDITOR
                UnityEngine.Debug.Log($"PoolDatabase.GetCalculatedHash: Type={enumTypeName}, Value={enumValueName}, TypeHash={typeHash}, ValueHash={valueHash}, Result={result}");
                #endif
                
                return result;
            }
            return 0;
        }
        
        /// <summary>
        /// Update the calculated hash (used by editor)
        /// </summary>
        public void UpdateCalculatedHash()
        {
            calculatedHash = GetCalculatedHash();
        }
        
        /// <summary>
        /// Get the stored calculated hash
        /// </summary>
        public readonly uint CalculatedHash => calculatedHash;

        /// <summary>
        /// Get the effective initial pool size, handling backward compatibility
        /// </summary>
        public readonly int GetInitialPoolSize()
        {
            // If new fields are set, use initialPoolSize
            if (initialPoolSize > 0 || maxPoolSize > 0)
                return initialPoolSize;

            // Fallback to legacy poolSize for backward compatibility
            return poolSize;
        }

        /// <summary>
        /// Get the effective maximum pool size, handling backward compatibility
        /// </summary>
        public readonly int GetMaxPoolSize()
        {
            // If new fields are set, use maxPoolSize
            if (initialPoolSize > 0 || maxPoolSize > 0)
                return maxPoolSize;

            // Fallback to legacy poolSize for backward compatibility
            return poolSize;
        }
        
        /// <summary>
        /// Check if this entry is valid
        /// </summary>
        public bool IsValid => !string.IsNullOrEmpty(enumTypeName) &&
                              !string.IsNullOrEmpty(enumValueName) &&
                              prefab != null &&
                              (poolSize >= 0 || initialPoolSize >= 0 || maxPoolSize >= 0);
    }

    /// <summary>
    /// ScriptableObject that stores all prefabs that should be pooled at runtime. Designers can
    /// create multiple databases if needed and assign one of them to a PoolingAuthoring.
    /// </summary>
    [CreateAssetMenu(menuName = "Data/Pool Database", fileName = "PoolDatabase")]
    public class PoolDatabase : ScriptableObject
    {
        [Header("Pool Entries")]
        [Tooltip("List of all poolable prefabs with their enum mappings")]
        #if UNITY_EDITOR && ODIN_INSPECTOR
        [ListDrawerSettings(ShowIndexLabels = true, ListElementLabelName = "GetEntryLabel")]
        [OnValueChanged("OnEntriesChanged")]
        #endif
        public List<PoolDatabaseEntry> entries = new();
        
        [Header("Settings")]
        [Tooltip("Default pool size for entries that have poolSize = 0")]
        [Min(1)] public int defaultPoolSize = 10;

        #if UNITY_EDITOR && ODIN_INSPECTOR
        [Button("Add New Pool Entry", ButtonSizes.Medium)]
        [PropertySpace(SpaceBefore = 10)]
        private void AddNewEntry()
        {
            entries.Add(new PoolDatabaseEntry());
        }

        [Button("Validate All Entries", ButtonSizes.Medium)]
        [PropertySpace(SpaceBefore = 5)]
        private void ValidateAllEntries()
        {
            ValidateDatabase();
        }

        [Button("Update All Hashes", ButtonSizes.Medium)]
        private void UpdateAllHashes()
        {
            for (int i = 0; i < entries.Count; i++)
            {
                var entry = entries[i];
                entry.UpdateCalculatedHash();
                entries[i] = entry;
            }
            UnityEditor.EditorUtility.SetDirty(this);
        }

        private string GetEntryLabel(PoolDatabaseEntry entry, int index)
        {
            if (!string.IsNullOrEmpty(entry.enumTypeName) && !string.IsNullOrEmpty(entry.enumValueName))
            {
                var typeName = entry.enumTypeName.Split('.').LastOrDefault() ?? entry.enumTypeName;
                return $"{typeName}.{entry.enumValueName}";
            }
            return $"Entry {index}";
        }

        private void OnEntriesChanged()
        {
            // Auto-update hashes when entries change
            for (int i = 0; i < entries.Count; i++)
            {
                var entry = entries[i];
                entry.UpdateCalculatedHash();
                entries[i] = entry;
            }
        }
        #endif
        
        /// <summary>
        /// Get entry by enum type and value names
        /// </summary>
        public PoolDatabaseEntry? GetEntry(string enumTypeName, string enumValueName)
        {
            foreach (var entry in entries)
            {
                if (entry.enumTypeName == enumTypeName && entry.enumValueName == enumValueName)
                    return entry;
            }
            return null;
        }
        
        /// <summary>
        /// Get entry by calculated hash
        /// </summary>
        public PoolDatabaseEntry? GetEntryByHash(uint hash)
        {
            foreach (var entry in entries)
            {
                if (entry.GetCalculatedHash() == hash)
                    return entry;
            }
            return null;
        }
        
        /// <summary>
        /// Get all entries for a specific enum type
        /// </summary>
        public List<PoolDatabaseEntry> GetEntriesForType(string enumTypeName)
        {
            var result = new List<PoolDatabaseEntry>();
            foreach (var entry in entries)
            {
                if (entry.enumTypeName == enumTypeName)
                    result.Add(entry);
            }
            return result;
        }
        
        /// <summary>
        /// Validate all entries and return any issues
        /// </summary>
        public List<string> ValidateEntries()
        {
            var issues = new List<string>();
            var hashSet = new HashSet<uint>();
            
            for (int i = 0; i < entries.Count; i++)
            {
                var entry = entries[i];
                
                if (!entry.IsValid)
                {
                    issues.Add($"Entry {i}: Invalid entry - missing enum type, value, or prefab");
                    continue;
                }
                
                var hash = entry.GetCalculatedHash();
                if (hashSet.Contains(hash))
                {
                    issues.Add($"Entry {i}: Duplicate hash detected for {entry.enumTypeName}.{entry.enumValueName}");
                }
                else
                {
                    hashSet.Add(hash);
                }
            }
            
            return issues;
        }
        
        /// <summary>
        /// Get effective initial pool size (uses defaultPoolSize if entry has no values set)
        /// </summary>
        public int GetEffectiveInitialPoolSize(PoolDatabaseEntry entry)
        {
            int initialSize = entry.GetInitialPoolSize();
            return initialSize > 0 ? initialSize : defaultPoolSize;
        }

        /// <summary>
        /// Get effective maximum pool size (uses defaultPoolSize if entry has no values set)
        /// </summary>
        public int GetEffectiveMaxPoolSize(PoolDatabaseEntry entry)
        {
            int maxSize = entry.GetMaxPoolSize();
            return maxSize > 0 ? maxSize : defaultPoolSize;
        }

        /// <summary>
        /// Get effective pool size (uses defaultPoolSize if entry poolSize is 0) - DEPRECATED
        /// </summary>
        [System.Obsolete("Use GetEffectiveInitialPoolSize and GetEffectiveMaxPoolSize instead")]
        public int GetEffectivePoolSize(PoolDatabaseEntry entry)
        {
            return entry.poolSize > 0 ? entry.poolSize : defaultPoolSize;
        }
        
        private void OnValidate()
        {
            // Update calculated hashes in editor
            for (int i = 0; i < entries.Count; i++)
            {
                var entry = entries[i];
                entry.UpdateCalculatedHash();
                entries[i] = entry;
            }
        }
        
        #if UNITY_EDITOR
        [ContextMenu("Validate Database")]
        private void ValidateDatabase()
        {
            var issues = ValidateEntries();
            if (issues.Count == 0)
            {
                Debug.Log($"PoolDatabase '{name}': All {entries.Count} entries are valid!");
            }
            else
            {
                Debug.LogWarning($"PoolDatabase '{name}': Found {issues.Count} issues:\n" + string.Join("\n", issues));
            }
        }
        
        [ContextMenu("Log All Hashes")]
        private void LogAllHashes()
        {
            Debug.Log($"PoolDatabase '{name}' hashes:");
            foreach (var entry in entries)
            {
                Debug.Log($"{entry.enumTypeName}.{entry.enumValueName} = {entry.GetCalculatedHash()}");
            }
        }
        #endif
    }
}

